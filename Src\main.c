/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "arm_math.h"
#include "printf.h"
#include "ad9833.h"
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
// 状态机定义
typedef enum {
    STATE_IDLE,         // 空闲状态
    STATE_SAMPLING,     // 采样状态 - 只采样，不做其他任何操作
    STATE_PROCESSING,   // FFT计算状态
    STATE_TRANSMITTING  // 数据发送状态
} SystemState;

// 标定状态机定义
typedef enum {
    CAL_IDLE,           // 空闲状态
    CAL_SAMPLING,       // 采样状态
    CAL_WAITING,        // 等待采样完成
    CAL_PROCESSING      // 处理数据状态
} CalibrationState;
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
#define FFT_SIZE 1024
// 采样率计算：APB1时钟45MHz，TIM3分频器0，周期45 -> 45MHz/(45+1) = 978.26kHz
// 为了达到1MHz，需要设置Period = 44 -> 45MHz/(44+1) = 1MHz
#define SAMPLE_RATE 1000000  // 1MHz采样率目标
// ADC转换参数
#define ADC_RESOLUTION 4096.0f  // 12位ADC: 2^12 = 4096
#define ADC_VREF 3.3f          // 参考电压3.3V
#define ADC_TO_VOLTAGE(adc_val) ((float32_t)(adc_val) * ADC_VREF / ADC_RESOLUTION)

// DDS频率响应测试参数
#define RESPONSE_START_FREQ 200         // 起始频率200Hz（调试用）
#define RESPONSE_FREQ_STEP 200          // 频率步进200Hz
#define RESPONSE_POINTS 1               // 测试点数（调试模式：只测试一个频率）

// DFT参数
#define MIN_DFT_SIZE 64                 // 最小DFT点数
#define MAX_DFT_SIZE 2048               // 最大DFT点数
#define MIN_CYCLES_PER_DFT 4            // DFT中最少包含的信号周期数
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
uint16_t adcBuffer[FFT_SIZE];
float32_t fftInput[FFT_SIZE * 2];  // 复数输入，实部+虚部
float32_t fftOutput[FFT_SIZE];
arm_rfft_fast_instance_f32 fftInstance;

volatile uint8_t adcComplete = 0;
volatile uint8_t outputDetailedData = 1;  // 控制是否输出详细数据，1=输出，0=仅摘要
SystemState currentState = STATE_IDLE;
volatile uint8_t transmitStage = 0;  // 发送阶段：0=原始数据，1=FFT结果，2=完成
volatile uint8_t samplingActive = 0;  // 采样活动标志
volatile uint32_t samplingStartTime = 0;  // 采样开始时间
volatile uint32_t samplingEndTime = 0;    // 采样结束时间
volatile uint8_t testMode = 0;  // 0=正常模式, 1=采样率测试模式（已禁用）

// DDS频率响应测试相关变量
volatile uint8_t responseMode = 1;  // 1=频率响应测试模式, 0=其他模式
volatile uint16_t currentResponsePoint = 0;  // 当前测试点索引
volatile uint8_t debugMode = 1;  // 1=调试模式，显示详细信息
volatile uint8_t singleFreqDebug = 1;  // 1=单频率完整调试模式

// 频率响应结果存储数组
float32_t amplitudeResponse[RESPONSE_POINTS];  // 幅度响应
float32_t phaseResponse[RESPONSE_POINTS];      // 相位响应(弧度)
float32_t frequencyList[RESPONSE_POINTS];      // 频率列表

// DFT相关变量
uint16_t currentDftSize = 0;  // 当前使用的DFT点数
float32_t dftInput[MAX_DFT_SIZE];       // DFT输入数据
float32_t dftOutput[MAX_DFT_SIZE * 2];  // DFT输出数据(复数)
arm_rfft_fast_instance_f32 dftInstance; // DFT实例
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void Process_FFT(void);
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc);
void StateMachine_Process(void);
void Transmit_Data_Chunk(void);
void Analyze_Signal_Quality(void);
void Frequency_Response_Process(void);
void Calculate_Optimal_DFT_Size(uint32_t freq_hz, uint16_t* dft_size);
void Calculate_Amplitude_Phase_Response(float32_t* amplitude, float32_t* phase, uint32_t target_freq);
void Output_All_Response_Results(void);
void Single_Frequency_Debug_Process(void);  // 单频率完整调试函数
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART3_UART_Init();
  MX_ADC1_Init();
  MX_TIM3_Init();
  MX_TIM8_Init();
  MX_ADC2_Init();
  MX_ADC3_Init();
  /* USER CODE BEGIN 2 */
  // 初始化DSP库的FFT实例
  arm_rfft_fast_init_f32(&fftInstance, FFT_SIZE);
  
  // 初始化AD9833 DDS
  AD9833_Init();
  AD9833_Write(0x0000);

  // 启动定时器，但状态机会控制ADC的启动
  HAL_TIM_Base_Start(&htim3);
  
  // 初始化频率响应测试数组
  for(uint16_t i = 0; i < RESPONSE_POINTS; i++) {
      amplitudeResponse[i] = 0;
      phaseResponse[i] = 0;
      frequencyList[i] = RESPONSE_START_FREQ + i * RESPONSE_FREQ_STEP;
  }

  printf("System Frequency Response Analyzer\r\n");
  printf("Sampling Rate: %lu Hz\r\n", SAMPLE_RATE);
  printf("Response Mode: %s\r\n", responseMode ? "FREQUENCY RESPONSE TEST" : "OTHER MODE");
  printf("Test Range: %dHz - %dHz (Step: %dHz)\r\n", 
         RESPONSE_START_FREQ, 
         RESPONSE_START_FREQ + (RESPONSE_POINTS-1) * RESPONSE_FREQ_STEP,
         RESPONSE_FREQ_STEP);
  printf("Total Points: %d\r\n", RESPONSE_POINTS);
  printf("DFT Size Range: %d - %d points (adaptive)\r\n", MIN_DFT_SIZE, MAX_DFT_SIZE);
  printf("===========================================\r\n");
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    /*
    // 测试模式已禁用
    if (testMode) {
        Simple_Sampling_Test();
        HAL_Delay(1);
    } else 
    */
    if (singleFreqDebug) {
        Single_Frequency_Debug_Process();
        HAL_Delay(10);
    } else if (responseMode) {
        Frequency_Response_Process();
        HAL_Delay(10);
    } else {
        // 默认运行频率响应测试
        printf("Starting frequency response test...\r\n");
        responseMode = 1;
        HAL_Delay(1000);
    }
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 144;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/*
// 原始FFT处理函数 - 已注释，使用新的Calculate_Amplitude_RMS替代
void Process_FFT(void) {
    uint32_t i;
    float32_t dcOffset = 0.0f;

    // 第一步：将ADC采样数据转换为电压值
    for (i = 0; i < FFT_SIZE; i++) {
        fftInput[i] = ADC_TO_VOLTAGE(adcBuffer[i]);
        dcOffset += fftInput[i];
    }

    // 第二步：计算并去除DC偏置
    dcOffset /= FFT_SIZE;
    for (i = 0; i < FFT_SIZE; i++) {
        fftInput[i] -= dcOffset;
    }

    // 第三步：执行实数FFT
    arm_rfft_fast_f32(&fftInstance, fftInput, fftOutput, 0);

    // 第四步：计算幅度谱(只计算前半部分，因为实数FFT结果对称)
    arm_cmplx_mag_f32(fftOutput, fftInput, FFT_SIZE / 2);

    // 第五步：正确的幅度归一化
    // DC分量 (bin 0) 需要除以N
    fftInput[0] = fftInput[0] / FFT_SIZE;

    // 其他频率分量需要除以N/2 (除了Nyquist频率)
    for (i = 1; i < FFT_SIZE / 2; i++) {
        fftInput[i] = fftInput[i] * 2.0f / FFT_SIZE;
    }

    // 添加DC偏置信息到结果中（用于显示）
    fftInput[0] = dcOffset;  // 将实际DC偏置存储在bin 0中
}
*/

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1 && samplingActive) {
        // 只在采样状态时设置完成标志
        adcComplete = 1;
        // 中断中不做任何其他操作，确保采样时序准确
    }
}

/*
// 原始状态机处理函数 - 已注释，使用新的DDS_Calibration_Process替代
void StateMachine_Process(void) {
    switch (currentState) {
        case STATE_IDLE:
            // 启动新的采样周期
            currentState = STATE_SAMPLING;
            samplingActive = 1;
            adcComplete = 0;
            samplingStartTime = HAL_GetTick();  // 记录采样开始时间

            // 在采样期间降低UART中断优先级，确保ADC采样不被打断
            HAL_NVIC_SetPriority(USART3_IRQn, 15, 0);  // 降低UART优先级

            HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, FFT_SIZE);
            break;

        case STATE_SAMPLING:
            // 采样状态：只检查采样完成，不做任何其他操作
            // 不发送数据，不计算，确保采样率准确
            if (adcComplete) {
                samplingEndTime = HAL_GetTick();  // 记录采样结束时间
                samplingActive = 0;
                currentState = STATE_PROCESSING;
                // 注意：这里不打印信息，避免影响采样时序
            }
            break;

        case STATE_PROCESSING:
            // 停止定时器，避免在处理期间继续触发ADC
            HAL_TIM_Base_Stop(&htim3);

            // 恢复UART中断优先级
            HAL_NVIC_SetPriority(USART3_IRQn, 0, 0);  // 恢复UART优先级

            Analyze_Signal_Quality();  // 先分析信号质量
            Process_FFT();
            currentState = STATE_TRANSMITTING;
            transmitStage = 0;

            // 计算实际采样时间和采样率
            uint32_t samplingDuration = samplingEndTime - samplingStartTime;
            float actualSampleRate = (float)FFT_SIZE * 1000.0f / samplingDuration;  // Hz

            printf("ADC DMA Complete - %d samples acquired\r\n", FFT_SIZE);
            printf("Sampling Duration: %lu ms\r\n", samplingDuration);
            printf("Actual Sample Rate: %.2f Hz (Target: %lu Hz)\r\n", actualSampleRate, SAMPLE_RATE);
            printf("Sample Rate Error: %.2f%%\r\n", (actualSampleRate - SAMPLE_RATE) * 100.0f / SAMPLE_RATE);
            break;

        case STATE_TRANSMITTING:
            Transmit_Data_Chunk();
            if (transmitStage >= 3) {  // 所有发送阶段完成
                currentState = STATE_IDLE;
                // 重新启动定时器，准备下一次采样
                HAL_TIM_Base_Start(&htim3);
            }
            break;
    }
}

void Transmit_Data_Chunk(void) {
    uint32_t i;
    float32_t frequency;
    
    switch (transmitStage) {
        case 0:  // 发送完整ADC原始数据
            printf("\r\n=== Complete ADC Raw Data (1024 samples) ===\r\n");
            printf("ADC Resolution: 12-bit (0-4095), Reference Voltage: %.1fV\r\n", ADC_VREF);
            for (i = 0; i < FFT_SIZE; i++) {
                if (i % 8 == 0) {
                    printf("\r\n[%4lu-%4lu]: ", i, (i + 7 < FFT_SIZE) ? i + 7 : FFT_SIZE - 1);
                }
                float32_t voltage = ADC_TO_VOLTAGE(adcBuffer[i]);
                printf("%4u(%.3fV) ", adcBuffer[i], voltage);
            }
            printf("\r\n=== End of Raw ADC Data ===\r\n");
            transmitStage = 1;
            break;
            
        case 1:  // 发送FFT分析结果
            {
                float32_t maxValue;
                uint32_t maxIndex;
                
                // 找到最大频率分量(跳过DC分量)
                arm_max_f32(&fftInput[1], (FFT_SIZE / 2) - 1, &maxValue, &maxIndex);
                maxIndex += 1;  // 调整索引
                frequency = (float32_t)maxIndex * SAMPLE_RATE / FFT_SIZE;
                
                printf("\r\n=== FFT Analysis Results ===\r\n");
                printf("DC Offset: %.6f V\r\n", fftInput[0]);
                printf("Peak Frequency: %.2f Hz (Bin %lu)\r\n", frequency, maxIndex);
                printf("Peak Amplitude: %.6f V (RMS)\r\n", maxValue);
                printf("Peak-to-Peak: %.6f V (estimated)\r\n", maxValue * 2.828f);  // √8 for sine wave

                // 分析10kHz附近的频率
                float32_t freq_10k = 10000.0f;
                uint32_t bin_10k = (uint32_t)(freq_10k * FFT_SIZE / SAMPLE_RATE + 0.5f);
                printf("10kHz Analysis: Bin %lu (%.2f Hz) -> %.6f V\r\n",
                       bin_10k, (float32_t)bin_10k * SAMPLE_RATE / FFT_SIZE, fftInput[bin_10k]);
                
                // 输出完整的FFT幅度谱数据 (512个有效频率bin)
                printf("\r\n=== Complete FFT Magnitude Spectrum (512 bins) ===\r\n");
                printf("Units: Voltage (V)\r\n");
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    if (i % 6 == 0) {
                        printf("\r\n[%3lu-%3lu]: ", i, (i + 5 < FFT_SIZE / 2) ? i + 5 : (FFT_SIZE / 2) - 1);
                    }
                    printf("%10.6f ", fftInput[i]);
                }
                printf("\r\n=== End of FFT Magnitude Data ===\r\n");
                
                // 输出频率对应表 (所有512个bin)
                printf("\r\n=== Complete Frequency Reference (All 512 bins) ===\r\n");
                printf("Format: Bin[Index]:Frequency(Hz)->Magnitude(V)\r\n");
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    frequency = (float32_t)i * SAMPLE_RATE / FFT_SIZE;
                    if (i % 4 == 0) {
                        printf("\r\n");
                    }
                    printf("Bin%3lu:%8.2fHz->%8.6fV  ", i, frequency, fftInput[i]);
                }
                printf("\r\n=== End of Frequency Reference ===\r\n");
                
                transmitStage = 2;
            }
            break;
            
        case 2:  // 发送前10个峰值频率
            printf("\r\n=== Top 10 Peak Frequencies ===\r\n");
            
            // 创建临时缓冲区用于查找峰值
            {
                static float32_t tempBuffer[FFT_SIZE / 2];
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    tempBuffer[i] = fftInput[i];
                }
                
                for (uint8_t rank = 1; rank <= 10; rank++) {
                    float32_t maxVal;
                    uint32_t maxIdx;
                    arm_max_f32(tempBuffer, FFT_SIZE / 2, &maxVal, &maxIdx);
                    frequency = (float32_t)maxIdx * SAMPLE_RATE / FFT_SIZE;
                    printf("%2d. Bin %3lu: %10.2f Hz -> Magnitude: %10.6f V\r\n", rank, maxIdx, frequency, maxVal);
                    tempBuffer[maxIdx] = 0;
                }
            }
            
            printf("\r\n=== Processing Complete ===\r\n");
            printf("========================================\r\n\r\n");
            transmitStage = 3;  // 标记完成
            break;
    }
}
*/

/*
// 采样测试函数已禁用
void Simple_Sampling_Test(void) {
    static uint8_t testState = 0;
    static uint32_t testStartTime = 0;
    static uint32_t testEndTime = 0;

    switch (testState) {
        case 0:  // 启动测试
            adcComplete = 0;
            samplingActive = 1;
            testStartTime = HAL_GetTick();
            printf("Starting simple sampling test...\r\n");
            HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, FFT_SIZE);
            testState = 1;
            break;

        case 1:  // 等待采样完成
            if (adcComplete) {
                testEndTime = HAL_GetTick();
                samplingActive = 0;
                HAL_ADC_Stop_DMA(&hadc1);

                uint32_t duration = testEndTime - testStartTime;
                float actualRate = (float)FFT_SIZE * 1000.0f / duration;
                float error = (actualRate - SAMPLE_RATE) * 100.0f / SAMPLE_RATE;

                printf("=== Simple Sampling Test Results ===\r\n");
                printf("Duration: %lu ms\r\n", duration);
                printf("Target Rate: %lu Hz\r\n", SAMPLE_RATE);
                printf("Actual Rate: %.2f Hz\r\n", actualRate);
                printf("Error: %.2f%%\r\n", error);

                // 显示前8个采样值
                printf("First 8 samples: ");
                for (int i = 0; i < 8; i++) {
                    printf("%u(%.3fV) ", adcBuffer[i], ADC_TO_VOLTAGE(adcBuffer[i]));
                }
                printf("\r\n================================\r\n\r\n");

                testState = 2;
            }
            break;

        case 2:  // 等待重新开始
            HAL_Delay(3000);  // 等待3秒
            testState = 0;    // 重新开始测试
            break;
    }
}
*/

void Analyze_Signal_Quality(void) {
    uint32_t i;
    float32_t voltage, sum = 0.0f, min_val = 5.0f, max_val = 0.0f;
    uint32_t min_adc = 4095, max_adc = 0;

    // 分析ADC原始数据
    for (i = 0; i < FFT_SIZE; i++) {
        voltage = ADC_TO_VOLTAGE(adcBuffer[i]);
        sum += voltage;

        if (voltage < min_val) min_val = voltage;
        if (voltage > max_val) max_val = voltage;
        if (adcBuffer[i] < min_adc) min_adc = adcBuffer[i];
        if (adcBuffer[i] > max_adc) max_adc = adcBuffer[i];
    }

    float32_t avg_voltage = sum / FFT_SIZE;
    float32_t peak_to_peak = max_val - min_val;

    printf("\r\n=== Signal Quality Analysis ===\r\n");
    printf("ADC Range: %u - %u (out of 0-4095)\r\n", min_adc, max_adc);
    printf("Voltage Range: %.3f - %.3f V\r\n", min_val, max_val);
    printf("Average Voltage: %.3f V (DC Offset)\r\n", avg_voltage);
    printf("Peak-to-Peak: %.3f V\r\n", peak_to_peak);
    printf("Signal Utilization: %.1f%% of ADC range\r\n",
           (float)(max_adc - min_adc) * 100.0f / 4095.0f);

    // 检查信号质量
    if (min_adc == 0 || max_adc == 4095) {
        printf("WARNING: Signal clipping detected!\r\n");
    }
    if (peak_to_peak < 0.1f) {
        printf("WARNING: Signal amplitude very low!\r\n");
    }
    if (avg_voltage < 0.1f || avg_voltage > 3.2f) {
        printf("WARNING: DC offset may be out of optimal range!\r\n");
    }

    printf("Expected for 1V offset, 1V p-p sine: DC=1V, Range=0.5-1.5V, P-P=1V\r\n");
    printf("===============================\r\n");
}

// 设置DDS频率
void Set_DDS_Frequency(uint32_t freq_hz) {
    AD9833_FreqSet((double)freq_hz);
    // 等待频率稳定
    HAL_Delay(10);
}

// 计算最优DFT点数
void Calculate_Optimal_DFT_Size(uint32_t freq_hz, uint16_t* dft_size) {
    // 计算每个周期需要的采样点数
    float32_t samples_per_cycle = (float32_t)SAMPLE_RATE / freq_hz;
    
    // 对于低频，确保至少有足够的频率分辨率
    // 频率分辨率 = 采样率 / DFT点数
    // 为了准确测量目标频率，频率分辨率应该远小于目标频率
    uint32_t min_points_for_resolution = SAMPLE_RATE / (freq_hz / 8);  // 频率分辨率至少是目标频率的1/8
    if (min_points_for_resolution > MAX_DFT_SIZE) {
        min_points_for_resolution = MAX_DFT_SIZE;
    }
    
    // 计算包含MIN_CYCLES_PER_DFT个周期所需的最小点数
    uint16_t min_points_for_cycles = (uint16_t)(samples_per_cycle * MIN_CYCLES_PER_DFT);
    
    // 取两者的最大值
    uint16_t min_points = (min_points_for_resolution > min_points_for_cycles) ? 
                         (uint16_t)min_points_for_resolution : min_points_for_cycles;
    
    // 向上调整到最接近的2的幂次
    uint16_t optimal_size = MIN_DFT_SIZE;
    while (optimal_size < min_points && optimal_size < MAX_DFT_SIZE) {
        optimal_size <<= 1;  // 乘以2
    }
    
    // 确保不超过最大限制
    if (optimal_size > MAX_DFT_SIZE) {
        optimal_size = MAX_DFT_SIZE;
    }
    
    // 确保不小于最小限制
    if (optimal_size < MIN_DFT_SIZE) {
        optimal_size = MIN_DFT_SIZE;
    }
    
    *dft_size = optimal_size;
    
    // 调试信息
    if (debugMode && currentResponsePoint < 10) {
        float32_t freq_resolution = (float32_t)SAMPLE_RATE / optimal_size;
        printf("  DEBUG: DFT Size Selection - Freq=%luHz, Cycles=%.2f, DFT=%d, FreqRes=%.2fHz\r\n",
               freq_hz, optimal_size / samples_per_cycle, optimal_size, freq_resolution);
    }
}

// 计算幅度和相位响应
void Calculate_Amplitude_Phase_Response(float32_t* amplitude, float32_t* phase, uint32_t target_freq) {
    uint32_t i;
    float32_t dcOffset = 0.0f;
    
    // 第一步：将ADC采样数据转换为电压值并计算DC偏置
    for (i = 0; i < currentDftSize; i++) {
        dftInput[i] = ADC_TO_VOLTAGE(adcBuffer[i]);
        dcOffset += dftInput[i];
    }
    dcOffset /= currentDftSize;
    
    // 第二步：去除DC偏置
    for (i = 0; i < currentDftSize; i++) {
        dftInput[i] -= dcOffset;
    }
    
    // 第三步：初始化DFT实例（根据当前DFT大小）
    arm_rfft_fast_instance_f32 currentDftInstance;
    if (arm_rfft_fast_init_f32(&currentDftInstance, currentDftSize) != ARM_MATH_SUCCESS) {
        printf("DFT init failed for size %d\r\n", currentDftSize);
        *amplitude = 0;
        *phase = 0;
        return;
    }
    
    // 第四步：执行实数DFT
    arm_rfft_fast_f32(&currentDftInstance, dftInput, dftOutput, 0);
    
    // 第五步：找到目标频率对应的bin
    float32_t freq_resolution = (float32_t)SAMPLE_RATE / currentDftSize;
    uint32_t target_bin = (uint32_t)(target_freq / freq_resolution + 0.5f);
    
    // 确保bin索引在有效范围内
    if (target_bin >= currentDftSize / 2) {
        target_bin = currentDftSize / 2 - 1;
    }
    
    // 第六步：提取目标频率的实部和虚部
    float32_t real_part, imag_part;
    if (target_bin == 0) {
        // DC分量
        real_part = dftOutput[0];
        imag_part = 0;
    } else {
        // AC分量 - 修正索引方式
        real_part = dftOutput[target_bin * 2];       // 实部
        imag_part = dftOutput[target_bin * 2 + 1];   // 虚部
    }
    
    // 第七步：计算幅度（峰值）
    float32_t magnitude = sqrtf(real_part * real_part + imag_part * imag_part);
    
    // 修正归一化方式
    if (target_bin == 0) {
        // DC分量
        magnitude = magnitude / currentDftSize;
    } else {
        // AC分量：对于实数FFT，需要乘以2然后除以N来得到正确的幅度
        magnitude = magnitude * 2.0f / currentDftSize;
    }
    
    // 第八步：计算相位（弧度）
    float32_t phase_rad = atan2f(imag_part, real_part);
    
    *amplitude = magnitude;
    *phase = phase_rad;
    
    // 调试信息输出
    if (debugMode && currentResponsePoint < 10) {  // 只显示前10个点的调试信息
        printf("  DEBUG: Freq=%luHz, DFT=%d, Bin=%lu, FreqRes=%.2fHz\r\n", 
               target_freq, currentDftSize, target_bin, freq_resolution);
        printf("  DEBUG: Real=%.6f, Imag=%.6f, Mag=%.6f, Phase=%.3f°\r\n",
               real_part, imag_part, magnitude, phase_rad * 180.0f / M_PI);
        printf("  DEBUG: Raw ADC range: %u-%u\r\n", 
               adcBuffer[0], adcBuffer[currentDftSize-1]);
    }
}

// 计算RMS幅度值
void Calculate_Amplitude_RMS(float32_t* amplitude) {
    uint32_t i;
    float32_t dcOffset = 0.0f;

    // 第一步：将ADC采样数据转换为电压值并计算DC偏置
    for (i = 0; i < FFT_SIZE; i++) {
        fftInput[i] = ADC_TO_VOLTAGE(adcBuffer[i]);
        dcOffset += fftInput[i];
    }
    dcOffset /= FFT_SIZE;

    // 第二步：去除DC偏置
    for (i = 0; i < FFT_SIZE; i++) {
        fftInput[i] -= dcOffset;
    }

    // 第三步：执行实数FFT
    arm_rfft_fast_f32(&fftInstance, fftInput, fftOutput, 0);

    // 第四步：计算幅度谱
    arm_cmplx_mag_f32(fftOutput, fftInput, FFT_SIZE / 2);

    // 第五步：正确的幅度归一化
    fftInput[0] = fftInput[0] / FFT_SIZE;  // DC分量
    for (i = 1; i < FFT_SIZE / 2; i++) {
        fftInput[i] = fftInput[i] * 2.0f / FFT_SIZE;  // AC分量
    }

    // 找到最大幅度分量（跳过DC）
    float32_t maxValue;
    uint32_t maxIndex;
    arm_max_f32(&fftInput[1], (FFT_SIZE / 2) - 1, &maxValue, &maxIndex);
    
    *amplitude = maxValue;  // 返回RMS幅度值
}

// 频率响应测试主处理函数
void Frequency_Response_Process(void) {
    static CalibrationState responseState = CAL_IDLE;
    static uint32_t currentFreq = 0;
    
    switch (responseState) {
        case CAL_IDLE:
            // 检查是否完成所有测试点
            if (currentResponsePoint >= RESPONSE_POINTS) {
                printf("\r\n=== Frequency Response Test Complete ===\r\n");
                printf("All %d frequency points tested.\r\n", RESPONSE_POINTS);
                printf("Starting output...\r\n");
                Output_All_Response_Results();
                responseMode = 0;  // 退出测试模式
                return;
            }
            
            // 设置当前频率点
            currentFreq = RESPONSE_START_FREQ + currentResponsePoint * RESPONSE_FREQ_STEP;
            
            // 计算最优DFT点数
            Calculate_Optimal_DFT_Size(currentFreq, &currentDftSize);
            
            Set_DDS_Frequency(currentFreq);
            responseState = CAL_SAMPLING;
            
            // 简单进度显示
            if (debugMode || (currentResponsePoint + 1) % 100 == 0 || currentResponsePoint == 0) {
                printf("Testing Point %d/%d: %luHz\r\n", 
                       currentResponsePoint + 1, RESPONSE_POINTS, currentFreq);
            }
            break;
            
        case CAL_SAMPLING:
            // 启动ADC采样（使用当前DFT大小）
            adcComplete = 0;
            samplingActive = 1;
            HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, currentDftSize);
            responseState = CAL_WAITING;
            break;
            
        case CAL_WAITING:
            // 等待ADC采样完成
            if (adcComplete) {
                samplingActive = 0;
                HAL_ADC_Stop_DMA(&hadc1);
                responseState = CAL_PROCESSING;
            }
            break;
            
        case CAL_PROCESSING:
            {
                float32_t amplitude = 0;
                float32_t phase = 0;
                Calculate_Amplitude_Phase_Response(&amplitude, &phase, currentFreq);
                
                // 直接保存结果，不做平均
                amplitudeResponse[currentResponsePoint] = amplitude;
                phaseResponse[currentResponsePoint] = phase;
                
                // 调试模式下显示结果
                if (debugMode && currentResponsePoint < 10) {
                    printf("  RESULT: Freq=%luHz, Amp=%.6fV, Phase=%.3f°\r\n", 
                           currentFreq, amplitude, phase * 180.0f / M_PI);
                }
                
                // 移到下一个频率点
                currentResponsePoint++;
                responseState = CAL_IDLE;
            }
            break;
    }
}

// 统一输出所有频率响应结果
void Output_All_Response_Results(void) {
    printf("\r\n=== Frequency Response Results ===\r\n");
    printf("Format: FREQ(Hz),AMPLITUDE(V),PHASE(degrees)\r\n");
    printf("==========================================\r\n");
    
    for (uint16_t i = 0; i < RESPONSE_POINTS; i++) {
        printf("%.0f,%.6f,%.3f\r\n", 
               frequencyList[i], 
               amplitudeResponse[i], 
               phaseResponse[i] * 180.0f / M_PI);
    }
    
    printf("==========================================\r\n");
    printf("=== %d points output complete ===\r\n", RESPONSE_POINTS);
}

// 单频率完整调试处理函数
void Single_Frequency_Debug_Process(void) {
    static uint8_t debugState = 0;
    static uint32_t debugFreq = RESPONSE_START_FREQ;  // 200Hz
    static uint16_t debugDftSize = 0;
    
    switch (debugState) {
        case 0:  // 初始化阶段
            printf("\r\n=== Single Frequency Debug Mode ===\r\n");
            printf("Target Frequency: %luHz\r\n", debugFreq);
            
            // 设置DDS频率
            printf("Step 1: Setting DDS frequency to %luHz...\r\n", debugFreq);
            Set_DDS_Frequency(debugFreq);
            
            // 计算最优DFT点数
            printf("Step 2: Calculating optimal DFT size...\r\n");
            Calculate_Optimal_DFT_Size(debugFreq, &debugDftSize);
            printf("  Selected DFT size: %d points\r\n", debugDftSize);
            printf("  Frequency resolution: %.2f Hz\r\n", (float)SAMPLE_RATE / debugDftSize);
            printf("  Expected bin: %.1f\r\n", (float)debugFreq * debugDftSize / SAMPLE_RATE);
            
            debugState = 1;
            break;
            
        case 1:  // 采样阶段
            printf("Step 3: Starting ADC sampling (%d points)...\r\n", debugDftSize);
            adcComplete = 0;
            samplingActive = 1;
            HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, debugDftSize);
            debugState = 2;
            break;
            
        case 2:  // 等待采样完成
            if (adcComplete) {
                samplingActive = 0;
                HAL_ADC_Stop_DMA(&hadc1);
                printf("Step 4: ADC sampling completed!\r\n");
                debugState = 3;
            }
            break;
            
        case 3:  // 数据存储和显示阶段
            printf("Step 5: Displaying raw ADC data...\r\n");
            printf("ADC Raw Data (first 20 and last 20 samples):\r\n");
            
            // 显示前20个采样点
            printf("First 20 samples:\r\n");
            for (int i = 0; i < 20 && i < debugDftSize; i++) {
                printf("ADC[%d] = %u (%.4fV)\r\n", i, adcBuffer[i], ADC_TO_VOLTAGE(adcBuffer[i]));
            }
            
            // 显示后20个采样点
            if (debugDftSize > 20) {
                printf("Last 20 samples:\r\n");
                int start = debugDftSize - 20;
                for (int i = start; i < debugDftSize; i++) {
                    printf("ADC[%d] = %u (%.4fV)\r\n", i, adcBuffer[i], ADC_TO_VOLTAGE(adcBuffer[i]));
                }
            }
            
            // 计算统计信息
            uint16_t minVal = 4095, maxVal = 0;
            uint32_t sum = 0;
            for (int i = 0; i < debugDftSize; i++) {
                if (adcBuffer[i] < minVal) minVal = adcBuffer[i];
                if (adcBuffer[i] > maxVal) maxVal = adcBuffer[i];
                sum += adcBuffer[i];
            }
            float avgVal = (float)sum / debugDftSize;
            
            printf("ADC Statistics:\r\n");
            printf("  Min: %u (%.4fV)\r\n", minVal, ADC_TO_VOLTAGE(minVal));
            printf("  Max: %u (%.4fV)\r\n", maxVal, ADC_TO_VOLTAGE(maxVal));
            printf("  Avg: %.1f (%.4fV)\r\n", avgVal, ADC_TO_VOLTAGE((uint16_t)avgVal));
            printf("  Peak-to-Peak: %u (%.4fV)\r\n", maxVal - minVal, ADC_TO_VOLTAGE(maxVal - minVal));
            
            debugState = 4;
            break;
            
        case 4:  // 计算阶段
            printf("Step 6: Performing DFT calculation...\r\n");
            
            float32_t amplitude = 0;
            float32_t phase = 0;
            
            // 使用全局的currentDftSize变量
            currentDftSize = debugDftSize;
            Calculate_Amplitude_Phase_Response(&amplitude, &phase, debugFreq);
            
            printf("Step 7: Final Results:\r\n");
            printf("  Target Frequency: %luHz\r\n", debugFreq);
            printf("  Calculated Amplitude: %.6fV\r\n", amplitude);
            printf("  Calculated Phase: %.3f degrees\r\n", phase * 180.0f / M_PI);
            
            printf("\r\n=== Debug Complete ===\r\n");
            printf("System will restart debug in 10 seconds...\r\n\r\n");
            
            debugState = 5;
            break;
            
        case 5:  // 等待重启
            HAL_Delay(10000);  // 等待10秒
            debugState = 0;    // 重新开始
            break;
    }
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
