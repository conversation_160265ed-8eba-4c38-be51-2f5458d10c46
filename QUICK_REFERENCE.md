# ADC采样优化 - 快速参考

## 🎯 主要问题
- 原始采样率远低于1MHz目标
- 采样期间有数据发送和计算干扰
- 时序不稳定

## ✅ 关键改进

### 1. 状态机重新设计
```c
STATE_IDLE → STATE_SAMPLING → STATE_PROCESSING → STATE_TRANSMITTING
```

### 2. 采样状态纯净化
- ❌ 不发送数据
- ❌ 不进行计算  
- ❌ 不打印信息
- ✅ 只检查ADC完成
- ✅ 最小延时(1ms)

### 3. 中断优先级优化
- ADC DMA: HIGH优先级
- 采样期间: 降低UART优先级
- 处理期间: 恢复UART优先级

### 4. 定时器控制
- 采样期间: 定时器运行
- 处理期间: 停止定时器
- 发送期间: 定时器停止

## 🧪 测试模式

### 启用测试模式
```c
volatile uint8_t testMode = 1;  // 在main.c中修改
```

### 测试输出示例
```
=== Simple Sampling Test Results ===
Duration: 1024 ms
Target Rate: 1000000 Hz
Actual Rate: 1000000.00 Hz
Error: 0.00%
First 8 samples: 648(0.522V) 1086(0.875V) ...
================================
```

## 📊 预期效果
- 采样率误差 < 1%
- 时序稳定性提升
- 10kHz信号FFT准确

## 🔧 快速调试

### 1. 检查采样率
观察串口输出的"Actual Sample Rate"和"Error"

### 2. 验证信号质量
检查采样值是否合理（0-4095范围）

### 3. FFT验证
正常模式下，10kHz信号应在bin 10附近出现峰值

## ⚙️ 配置验证
- 系统时钟: 180MHz
- APB1时钟: 45MHz  
- TIM3周期: 44
- 采样率: 45MHz/(44+1) = 1MHz ✅

## 🚨 故障排除
1. **采样率偏差大**: 检查时钟配置
2. **数据异常**: 检查信号源和ADC设置
3. **FFT结果错误**: 确认采样率准确性

## 📝 使用步骤
1. 编译烧录固件
2. 设置testMode=1进行采样率测试
3. 验证采样率准确性
4. 设置testMode=0进行正常FFT分析
5. 观察10kHz信号的FFT结果
