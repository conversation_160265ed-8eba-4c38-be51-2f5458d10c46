#include "ad9833.h"
#include "stm32f4xx_hal.h"  // 为HAL_Delay添加引用

void AD9833_Init(void)
{
    AD9833_FSC_H;
    AD9833_SCK_H;
    // 初始化复位状态
    AD9833_Write(0x0100); // 复位AD9833
    HAL_Delay(1);
}

void AD9833_Write(unsigned short word)
{
    short i;
    unsigned short w;
    AD9833_FSC_L;
    for(i=15;i>=0;i--)
    {
        w=word>>i;
        if(w&0x0001){AD9833_SDA_H;}
        else{AD9833_SDA_L;}
        AD9833_SCK_L;
        AD9833_SCK_H;
    }
    AD9833_FSC_H;
}

void AD9833_FreqSet(double freq)
{
    unsigned short freq_LSB,freq_MSB; 
    double freq_mid,freq_DATA;
    long int freq_hex;
    
    // 25MHz参考频率，计算频率寄存器值
    // FREQ_REG = (freq * 2^28) / 25MHz
    freq_mid = 268435456.0 / 25000000.0;  // 2^28 / 25MHz = 10.73741824
    freq_DATA = freq * freq_mid;
    freq_hex = (long int)freq_DATA;
    
    // 分割成14位的高低位
    freq_LSB = freq_hex & 0x3fff;         // 低14位
    freq_MSB = (freq_hex >> 14) & 0x3fff; // 高14位
    
    // 添加FREQ0寄存器标识位
    freq_LSB = freq_LSB | 0x4000;
    freq_MSB = freq_MSB | 0x4000;

    // 写入控制寄存器和频率数据
    AD9833_Write(0x2100); // 控制字：B28=1, RESET=1（连续写入模式）
    AD9833_Write(freq_LSB);  // 写入频率低位
    AD9833_Write(freq_MSB);  // 写入频率高位
    AD9833_Write(0x2000); // 控制字：B28=1, RESET=0（结束复位，开始输出）
}
