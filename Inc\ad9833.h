#ifndef _AD9833_H
#define _AD9833_H

#include "main.h"

#define AD9833_FSC_H HAL_GPIO_WritePin(AD9833_FSC_GPIO_Port,AD9833_FSC_Pin,GPIO_PIN_SET)
#define AD9833_FSC_L HAL_GPIO_WritePin(AD9833_FSC_GPIO_Port,AD9833_FSC_Pin,GPIO_PIN_RESET)
#define AD9833_SCK_H HAL_GPIO_WritePin(AD9833_SCK_GPIO_Port,AD9833_SCK_Pin,GPIO_PIN_SET)
#define AD9833_SCK_L HAL_GPIO_WritePin(AD9833_SCK_GPIO_Port,AD9833_SCK_Pin,GPIO_PIN_RESET)
#define AD9833_SDA_H HAL_GPIO_WritePin(AD9833_SDA_GPIO_Port,AD9833_SDA_Pin,GPIO_PIN_SET)
#define AD9833_SDA_L HAL_GPIO_WritePin(AD9833_SDA_GPIO_Port,AD9833_SDA_Pin,GPIO_PIN_RESET)

void AD9833_Init(void);
void AD9833_Write(unsigned short word);
void AD9833_FreqSet(double freq);

#endif
