/*
 * retarget.c
 * 将 printf 重定向到 USART3
 */
#include "stm32f4xx_hal.h"
#include "main.h"
#include <errno.h>
#include "printf.h"

#ifndef EOF
#define EOF (-1)
#endif

extern UART_HandleTypeDef huart3;

int __io_putchar(int ch)
{
    uint8_t c = (uint8_t)ch;
    if (HAL_UART_Transmit(&huart3, &c, 1, HAL_MAX_DELAY) != HAL_OK)
        return EOF;
    return ch;
}

int __io_getchar(void)
{
    uint8_t ch = 0;
    if (HAL_UART_Receive(&huart3, &ch, 1, HAL_MAX_DELAY) != HAL_OK)
        return EOF;
    return ch;
} 